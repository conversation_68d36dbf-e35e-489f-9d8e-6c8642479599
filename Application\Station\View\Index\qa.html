<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/css.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
    <link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
    <script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
    <title>问答学堂</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
        }

        /* 一级导航 */
          /* 修改导航容器样式 */
          .nav-container {
            margin: 0 12px;
            background: #fff;
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
            /* 隐藏溢出内容 */
            overflow: hidden;
        }

        .nav-scroll {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            gap: 20px;
            /* 隐藏滚动条关键代码 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */
            padding-bottom: 10px; /* 补偿隐藏的滚动条空间 */
            margin-bottom: -10px; /* 抵消补偿的padding */
        }

        /* Chrome/Safari隐藏滚动条 */
        .nav-scroll::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            background: transparent;
        }

        .nav-item {
            flex: 0 0 auto;
            font-size: 15px;
            color: #666;
            padding: 8px 0;
            position: relative;
            transition: color 0.3s;
        }

        .nav-item.active {
            color: #07C160;
            font-weight: 500;
        }

        .nav-item.active::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: #07C160;
        }

        /* 二级内容区 */
        .content-container {
            padding: 12px;
        }

        .section-title {
            font-size: 14px;
            color: #999;
            padding: 5px;
        }

        /* 问答列表 */
        .question-list {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.06);
        }

        .question-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f5f5f5;
            flex-direction: column;
        }

        .question-item:last-child {
            border-bottom: none;
        }

        .question-text {
            flex: 1;
            font-size: 16px;
            color: #333;
        }

         /* 新增样式 */
         .question-header {
            cursor: pointer;
            display: flex;
            align-items: center;
            width: 100%;
        }
        .answer-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            padding: 0 16px;
            color: #666;
            line-height: 1.6;
        }
        .arrow-icon {
            transition: transform 0.3s;
        }
        .expanded .arrow-icon {
            transform: rotate(90deg);
        }
        .expanded + .answer-content {
            max-height: 1000px; /* 根据实际内容调整 */
            padding: 16px;
        }


        .arrow-icon {
            width: 20px;
            height: 20px;
            margin-left: 10px;
            background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 24 24" fill="%23999" xmlns="http://www.w3.org/2000/svg"><path d="M9.29 15.88L13.17 12 9.29 8.12c-.39-.39-.39-1.02 0-1.41.39-.39 1.02-.39 1.41 0l4.59 4.59c.39.39.39 1.02 0 1.41l-4.59 4.59c-.39.39-1.02.39-1.41 0-.38-.39-.39-1.03 0-1.42z"/></svg>');
        }
        .question-item {
        transition: transform 0.2s;
         }
        .question-item:active {
        transform: scale(0.98);
         }

          /* 新增二级导航样式 */
        .sub-nav-container {
            margin: 0 12px;
            background: #f8f8f8;
            border-bottom: 1px solid #e7e7e7;
            position: sticky;
            z-index: 9;
            /* 隐藏溢出内容 */
            overflow: hidden;
            padding: 5px;
        }
        
        .sub-nav-scroll {
            display: none; /* 默认隐藏 */
            flex-wrap: nowrap;
            padding: 5px;
            gap: 15px;

        }

        /* Chrome/Safari隐藏滚动条 */
        .sub-nav-scroll::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            background: transparent;
      
        }
        .sub-nav-item {
            flex: 0 0 auto;
            font-size: 14px;
            color: #666;
            padding: 6px;
            border-radius: 6px;
            background: #fff;
            border: 1px solid #eee;
            transition: all 0.2s;
        }
        .sub-nav-item.active {
            color: #07C160;
            border-color: #07C160;
            background: rgba(7,193,96,0.05);
        }

        /* 调整原有内容间距 */
        .content-container {
            padding-top: 8px;
        }
        /* 搜索容器 */
        .search-box {
            padding: 12px 15px;
            background: #ffffff;
            border-bottom: 1px solid #ededed;
            position: relative;
        }

        /* 输入框样式 */
        .search-box input {
            width: 100%;
            height: 36px;
            padding: 0 40px 0 15px;
            border: 1px solid #e7e7e7;
            border-radius: 20px;
            font-size: 15px;
            color: #333;
            background: #f8f8f8;
            transition: all 0.3s;
        }

        /* 输入框聚焦状态 */
        .search-box input:focus {
            outline: none;
            border-color: #07C160;
            background: #fff;
            box-shadow: 0 2px 8px rgba(7, 193, 96, 0.1);
        }

        /* 占位符文字 */
        .search-box input::placeholder {
            color: #999;
            font-weight: 300;
        }

        /* 搜索图标 */
        .search-icon {
            position: absolute;
            right: 28px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
        }

        /* 夜间模式适配 */
        @media (prefers-color-scheme: dark) {
            .search-box {
                background: #1a1a1a;
                border-color: #333;
            }
            .search-box input {
                background: #2c2c2c;
                border-color: #404040;
                color: #ddd;
            }
            .search-icon {
                filter: invert(0.8);
            }
        }


                /* 返回箭头样式 */
                .back-arrow {
            position: fixed;
            top: 165px;
            left: 3px;
            width: 40px;
            height: 40px;
            z-index: 9999;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            transition: all 0.2s;
        }

        .back-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            width: 12px;
            height: 12px;
            border-left: 2px solid #333;
            border-bottom: 2px solid #333;
            transform: translateY(-50%) rotate(45deg);
        }
    </style>
</head>
<body>
<!-- 返回箭头代码 -->
<div class="back-arrow" onclick="goBack()"></div>
<include file="headers_aq"/>
    <div class="nav-container">
        <!-- 在nav-container下方添加 -->
        <div class="search-box">
            <input type="text" id="kwd" placeholder="搜索问题...">
            <img src="/static/stations/images/search-icon.svg" class="search-icon" >
        </div>
        
            <div class="nav-scroll" id="navScroll">
                <php>
                    $num = 0;
                    foreach ($list as $categoryRow) {
                    $num++;
                </php>
                <div class="nav-item {:($type == $categoryRow['id'] || ($type == 0 && $num == 1)) ? 'active' : ''}">{:$categoryRow['title']}</div>
                <php>}</php>
            </div>
      
    </div>

    <!-- 新增二级导航 -->
<!--    <div class="sub-nav-container">-->
<!--        <div class="sub-nav-scroll" id="subNavScroll"></div>-->
<!--    </div>-->

    <div class="content-container">
        <!--div class="section-title">常用问题</div-->
        <!-- 修改此处添加questionList ID -->
        <div class="question-list" id="questionList"></div>
    </div>

    <script>
        var url = '{:U("index/qacontent")}'
        var kwd = '{$kwd}';
        // 数据结构
        let navData = {:$navData};
        let oldnavData = {:$navData};
        var categoryName = "";
        var activecategory = $('#navScroll').find('.active').html();
        $('#kwd').blur(function () {
            var newKwd = $(this).val();
            if (newKwd != kwd) {
                console.log("xxx")
                kwd = newKwd
                navData = searchAndUpdateQuestions(oldnavData, newKwd);
                var activecategorys = $('#navScroll').find('.active').html();
                console.log(activecategorys)
                console.log(navData)
                updateSubNav(activecategorys);

            }
        })


        /**
         * 模糊搜索并更新questions结构的实现方案
         * @param {Object} originalData 原始数据
         * @param {string} keyword 搜索关键字
         * @returns {Object} 更新后的新JSON对象
         */
        function searchAndUpdateQuestions(originalData, keyword) {
            const result = JSON.parse(JSON.stringify(originalData)); // 深拷贝原始结构

            Object.keys(result).forEach(mainCategory => {
                const category = result[mainCategory];
                const newQuestions = {};

                // 遍历子分类
                Object.keys(category.questions).forEach(subCategory => {
                    // 执行模糊匹配筛选
                    const filtered = category.questions[subCategory].filter(item =>
                        item.name.toLowerCase().includes(keyword.toLowerCase())
                    );

                    // 仅保留非空子分类
                    if (filtered.length > 0) {
                        newQuestions[subCategory] = filtered;
                    }
                });

                // 更新questions结构（保留主分类框架）
                category.questions = newQuestions;
            });

            return result;
        }

        // 导航交互
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                // 移除所有激活状态
                navItems.forEach(nav => nav.classList.remove('active'));
                // 添加当前激活
                item.classList.add('active');
                // 自动滚动到可见区域
                item.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            });
        });



        // 初始化元素
        const mainNavItems = document.querySelectorAll('.nav-item');
        const subNavScroll = document.getElementById('subNavScroll');
        const questionList = document.getElementById('questionList');

        // 一级导航点击处理
        mainNavItems.forEach(item => {
            item.addEventListener('click', function() {
                mainNavItems.forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
                this.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                updateSubNav(this.textContent);
            });
        });



        // 更新二级导航
        function updateSubNav(category) {
            // subNavScroll.innerHTML = '';
            // subNavScroll.style.display = 'flex';
            console.log(category)
            // subNavScroll.appendChild(subItem);
            updateQuestions(category);


            // navData[category].subs.forEach(sub => {
            //     const subItem = document.createElement('div');
            //     subItem.className = 'sub-nav-item';
            //     subItem.textContent = sub;
            //
            //     // 添加点击处理
            //     subItem.addEventListener('click', function() {
            //         Array.from(subNavScroll.children).forEach(item =>
            //             item.classList.remove('active'));
            //         this.classList.add('active');
            //         this.scrollIntoView({
            //             behavior: 'smooth',
            //             block: 'nearest',
            //             inline: 'center'
            //         });
            //         updateQuestions(category, sub);
            //     });
            //
            //     subNavScroll.appendChild(subItem);
            // });

            // 默认激活第一个子项
            // if (subNavScroll.firstChild) {
            //     subNavScroll.firstChild.classList.add('active');
            //     updateQuestions(category, navData[category].subs[0]);
            // }
        }

        // 修改updateQuestions函数
        function updateQuestions(category) {
            if (navData[category].questions[category] != undefined) {
                questionList.innerHTML = navData[category].questions[category]
                    .map(q => `
                    <div class="question-item">
                        <div class="question-header" data-id="${q.id}">
                            <span class="question-text">${q.name}</span>
                            <div class="arrow-icon"></div>
                        </div>
                        <div class="answer-content"></div>
                    </div>
                `).join('');
            } else {
                questionList.innerHTML = '';
            }

        // 添加点击事件监听
            document.querySelectorAll('.question-header').forEach(header => {
                header.addEventListener('click', async function() {
                    const isExpanded = this.classList.contains('expanded');
                    const content = this.nextElementSibling;
                    
                    // 关闭所有其他项目
                    document.querySelectorAll('.question-header').forEach(h => {
                        if (h !== this) {
                            h.classList.remove('expanded');
                            h.nextElementSibling.style.maxHeight = '0';
                            h.nextElementSibling.innerHTML = '';
                        }
                    });

                    // 切换当前状态
                    if (!isExpanded) {
                        this.classList.add('expanded');
                        // 动态加载内容
                        if (!content.dataset.loaded) {
                            try {
                                const response = await fetch(`${url}?id=${this.dataset.id}`);
                                content.innerHTML = await response.text();
                                content.dataset.loaded = true;
                            } catch (error) {
                                content.innerHTML = '内容加载失败，请稍后再试';
                            }
                        }
                        content.style.maxHeight = content.scrollHeight + 'px';
                    } else {
                        this.classList.remove('expanded');
                        content.style.maxHeight = '0';
                    }
                });
            });
        }        

        function myFunction(num,index,arr) {

        }

        var noticeSwiper = new Swiper('.noticeSwiper', {
            direction: 'vertical',
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        })
        var noticeSwiper = new Swiper('.page0106Swiper', {
            pagination: {
                el: ".swiper-pagination",
            },

            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            }
        });

        // 初始化
        updateSubNav(activecategory);
        document.querySelector('.nav-item.active').scrollIntoView({ inline: 'center' });

            // 返回逻辑（优先返回历史记录，失败则跳转首页）
       function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/'; // 修改为你的默认回退地址
            }
        }
    </script>
</body>
</html>