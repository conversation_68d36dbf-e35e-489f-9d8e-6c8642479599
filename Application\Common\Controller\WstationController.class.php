<?php

namespace Common\Controller;

use Think\Controller;

/**
 * 商户后台管理控制
 */
class WstationController extends BaseController
{
    public $userRow = [];
    public $mod = 1; // 1 直接授权， 2 代理授权
    public $conf = [];

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        $this->setConf([
            'number' => '2',
            'WX_ID' => 'wxbe5ef2f3c4081630',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wxbe5ef2f3c4081630',
            'WX_APPSECRET' => 'd31659831cf47f6fab621e7141ddb61a',
        ]);

        if (C('LOCAL_DEBUG')) {
            $user = D('User')->find(68);
            session('openid2', $user['openid']);
            session('wx.user_id2', $user['id']);
            return;
        }
        if (!isWX()) {
            header("Content-type: text/html; charset=utf-8");
            die('请通过微信使用此功能 :)');
        }
    }

    /**
     * 检查招就办禁用状态
     * @param int $serviceStationId 服务站ID
     * @return bool true=已禁用，false=正常
     */
    protected function checkZsbDisabled($serviceStationId)
    {
        if (!$serviceStationId) {
            return false;
        }

        $serviceStation = D("ServiceStation")->where([
            'id' => $serviceStationId,
            'zsb_type' => 2
        ])->field('is_disabled')->find();

        return $serviceStation ? (bool)$serviceStation['is_disabled'] : false;
    }


    public function setConf($conf)
    {
        $this->conf = $conf;
    }

    /**
     * 微信code方式授权，获取 openid , scope:snsapi_base
     */
    public function codeAuth($conf = [])
    {
        if (!$conf) {
            $conf = $this->conf;
        }
        $suf = isset($conf['number']) ? $conf['number'] : '';
        if (!$this->authed($suf)) {
            vendor('LaneWeChat.lanewechat');
            if (!$code = I("get.code")) {
                \LaneWeChat\Core\Base::init($conf);
                $redirect_uri = preg_replace('/code=\w+\&?/', '', __HOST__ . $_SERVER['REQUEST_URI']);
                $redirect_uri = preg_replace('/\&?state=\w+\&?/', '', $redirect_uri);
                $state = (int)$conf['number'] > 10 ? $conf['number'] : (int)$conf['number'] + 1;
                if ($this->mod == 2) {
                    $state = base64_encode($redirect_uri);
                    $c_dm = D('Conf')->C('SYS_OUTER_DM_AUTH');
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri($c_dm), $state);
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($redirect_uri, $state);
                }
            } else {
                $row = [];
                \LaneWeChat\Core\Base::init($conf);
                unset($_GET['code']);
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);
                if ($res['errcode']) {
                    dolog('error/wx/authcode', "code:$code, result:" . json_encode($res) . ' ' . (int)$conf['number'] . $_SERVER['HTTP_USER_AGENT']);
                    die('Failed to get openid, errcode:' . $res['errcode']); // 授权失败,记录日志
                } else {
                    $num = $conf['number'] > 10 ? 9 : $conf['number'];
                    if ($num == 9) {
                        $str = 'openid' . $num;
                        session($str, $res['openid']);
                    } else {
                        $str = 'openid2';
                        session($str, $res['openid']);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 微信code方式授权，scope:snsapi_userinfo
     */
    public function codeAuth2()
    {
        if (!$this->loggedIn()) {
            vendor('LaneWeChat.lanewechat');
            $conf = $this->conf;
            $url = __HOST__ . $_SERVER['REQUEST_URI'];
            $code = $_REQUEST['code'];

            if (!empty($_REQUEST['code']) && cookie('curPath')) {
                unset($_REQUEST['code']);
                $url = cookie('curPath');
                session('auth2code', $code);
                $code = false;
                cookie('curPath', null);
            }
            \LaneWeChat\Core\Base::init($conf);
            if (!$code) {
                if ($this->mod == 2) {
                    $state = base64_encode($url);
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri(D('Conf')->C('SYS_OUTER_DM_AUTH')), $state, 'snsapi_userinfo');
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($url, '1', 'snsapi_userinfo');
                }
                exit;
            } else {
                unset($_GET['code']);
                //dolog('wx/authcode', "referer:{$_SERVER["HTTP_REFERER"]}, return data:".json_encode(I("get.")));
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);

                if ($res['errcode']) {
                    dolog('error/wx/authcode3', "result:" . json_encode($res));
                } else {
                    session('openid', $res['openid']);
                    D("User")->init($res['openid'], $res['access_token'], 2); // 初始化用户表
                }
            }
            noCodeJump();

        }
    }

    /**
     * 微信端授权登录
     * @return bool
     */
    public function login($auth = true)
    {
        if ($this->loggedIn()) return true;
        if (!I("get.code")) {
            cookie('curPath', __HOST__ . $_SERVER['REQUEST_URI'], 60);
        }
        $res = $this->codeAuth();
        if ($res) {
            $user = D("User")->where(['openid' => session('openid')])->find();
            // 目前只需授权一次，获取用户信息
            if ($user && !empty($user['nickname'])) {
                session('wx.user_id', $user['id']);
                return true;
            } else {
                if ($auth === true) $this->codeAuth2();
            }
        }
    }

    /**
     * 是否已授权
     */
    public function authed()
    {
        return session('openid2') ? true : false;
    }

    /**
     * 是否已登录
     */
    public function loggedIn()
    {
        return session('wx.user_id2') ? true : false;
    }

    /**
     * 登出，清除session
     */
    public function logout()
    {
        session(null);
    }

    /**
     * 重写display方法 - 临时禁用所有微信功能
     */
    protected function display($templateFile='', $charset='', $contentType='', $content='', $prefix='')
    {
        // 临时完全禁用所有微信相关功能，确保页面正常工作
        // 调用父类display方法
        parent::display($templateFile, $charset, $contentType, $content, $prefix);
    }

    /**
     * 输出微信隐藏分享按钮的JavaScript脚本 - 临时禁用
     */
    private function outputWechatHideShareScript()
    {
        // 临时完全禁用此功能，避免影响页面正常操作
        return;

        /*
        // 原始功能代码已注释，等页面恢复正常后再启用
        try {
            $isLocalEnv = C('LOCAL_DEBUG') || strpos($_SERVER['HTTP_HOST'], 'localhost') !== false;

            if ($isLocalEnv) {
                // 本地开发环境，只输出调试信息，不执行任何可能干扰页面的操作
                echo '<script>
                console.log("本地开发环境 - 微信分享隐藏功能已禁用");
                console.log("User-Agent:", navigator.userAgent);
                console.log("是否微信环境:", /MicroMessenger/i.test(navigator.userAgent));
                </script>';
                return;
            }

            // 只在生产环境中执行微信JS-SDK功能
            $wxConfig = getWxConfig();
            if (!$wxConfig || !isset($wxConfig['signature'])) {
                echo '<script>console.log("微信配置获取失败，无法隐藏分享按钮");</script>';
                return;
            }

            // 输出微信JS-SDK脚本
            echo '<script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>';
            echo '<script>
            // 微信分享按钮隐藏功能（仅生产环境）
            console.log("生产环境 - 开始配置微信JS-SDK隐藏分享功能");

            wx.config({
                debug: false,
                appId: "' . $this->conf['WX_APPID'] . '",
                timestamp: ' . $wxConfig['timestamp'] . ',
                nonceStr: "' . $wxConfig['noncestr'] . '",
                signature: "' . $wxConfig['signature'] . '",
                jsApiList: ["hideMenuItems"]
            });

            wx.ready(function() {
                console.log("微信JS-SDK配置成功，开始隐藏分享按钮");
                wx.hideMenuItems({
                    menuList: [
                        "menuItem:share:appMessage",    // 发送给朋友
                        "menuItem:share:timeline",      // 分享到朋友圈
                        "menuItem:share:qq",            // 分享到QQ
                        "menuItem:share:weiboApp",      // 分享到微博
                        "menuItem:share:QZone",         // 分享到QQ空间
                        "menuItem:share:facebook"       // 分享到Facebook
                    ],
                    success: function() {
                        console.log("分享按钮隐藏成功");
                    },
                    fail: function(res) {
                        console.log("分享按钮隐藏失败:", res);
                    }
                });
            });

            wx.error(function(res) {
                console.log("微信JS-SDK配置失败:", res);
            });
            </script>';

        } catch (Exception $e) {
            // 静默处理错误，不影响页面功能
            \Think\Log::write('微信隐藏分享功能异常：' . $e->getMessage(), 'WARN');
        }
        */
    }
}
